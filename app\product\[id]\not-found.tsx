import Link from "next/link"
import { Package, ArrowLeft } from "lucide-react"

export default function NotFound() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center">
        <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Product Not Found</h1>
        <p className="text-lg text-gray-600 mb-8">
          Sorry, we couldn't find the product you're looking for. It may have been removed or doesn't exist.
        </p>
        <div className="space-y-4">
          <Link
            href="/shop"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Shop
          </Link>
          <div>
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              Go to Homepage
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
