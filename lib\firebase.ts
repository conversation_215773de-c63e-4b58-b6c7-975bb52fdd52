import { initializeApp, getApps, getApp } from "firebase/app"
import { getFirestore } from "firebase/firestore"
import { getAuth } from "firebase/auth"

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
}

// Validate Firebase configuration
function validateFirebaseConfig() {
  const requiredValues = [
    firebaseConfig.apiKey,
    firebaseConfig.authDomain,
    firebaseConfig.projectId,
    firebaseConfig.storageBucket,
    firebaseConfig.messagingSenderId,
    firebaseConfig.appId,
  ]

  const missingValues = requiredValues.filter((value) => !value)

  if (missingValues.length > 0) {
    console.warn("Missing Firebase configuration values")
    return false
  }

  return true
}

// Initialize Firebase
let app
let db
let auth

try {
  if (validateFirebaseConfig()) {
    // Initialize Firebase app (avoid duplicate initialization)
    app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp()

    // Initialize Firestore
    db = getFirestore(app)

    // Initialize Auth
    auth = getAuth(app)

    console.log("Firebase initialized successfully")
  } else {
    console.warn("Firebase not initialized due to missing configuration")
  }
} catch (error) {
  console.error("Error initializing Firebase:", error)
}

export { db, auth, app }
export const isFirebaseConfigured = !!db && !!auth
