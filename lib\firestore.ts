import { collection, getDocs, doc, getDoc } from "firebase/firestore"
import { db, isFirebaseConfigured } from "./firebase"
import type { Product, ProductReview } from "./types"

// Helper function to convert Firestore data to plain objects
function convertFirestoreData(data: any): any {
  const converted = { ...data }
  
  // Convert Firestore Timestamps to ISO strings
  Object.keys(converted).forEach(key => {
    if (converted[key] && typeof converted[key].toDate === 'function') {
      converted[key] = converted[key].toDate().toISOString()
    }
  })
  
  return converted
}

// Enhanced mock data with more details
const mockProducts: Product[] = [
  {
    id: "1",
    name: "Wireless Headphones",
    image: "/wireless-headphones.png",
    price: 99.99,
    description: "High-quality wireless headphones with noise cancellation and long battery life.",
    category: "Electronics",
    brand: "AudioTech",
    inStock: true,
    stockQuantity: 25,
    images: [
      "/wireless-headphones.png",
      "/placeholder-j2x4s.png",
      "/folded-headphones.png",
      "/headphones-case.png",
    ],
    specifications: {
      "Battery Life": "30 hours",
      "Charging Time": "2 hours",
      "Bluetooth Version": "5.0",
      Weight: "250g",
      "Driver Size": "40mm",
      "Frequency Response": "20Hz - 20kHz",
    },
    features: [
      "Active Noise Cancellation",
      "Quick Charge (15 min = 3 hours)",
      "Touch Controls",
      "Voice Assistant Compatible",
      "Foldable Design",
      "Premium Materials",
    ],
    rating: 4.5,
    reviewCount: 128,
  },
  {
    id: "2",
    name: "Smart Watch",
    image: "/smartwatch-lifestyle.png",
    price: 199.99,
    description: "Feature-rich smartwatch with fitness tracking, notifications, and GPS.",
    category: "Wearables",
    brand: "TechWear",
    inStock: true,
    stockQuantity: 15,
    images: [
      "/smartwatch-lifestyle.png",
      "/black-smartwatch.png",
      "/silver-smartwatch.png",
      "/diverse-smartwatch-bands.png",
    ],
    specifications: {
      Display: '1.4" AMOLED',
      "Battery Life": "7 days",
      "Water Resistance": "5ATM",
      Storage: "4GB",
      Connectivity: "Bluetooth 5.0, WiFi",
      Sensors: "Heart Rate, GPS, Accelerometer",
    },
    features: [
      "Heart Rate Monitoring",
      "GPS Tracking",
      "Sleep Tracking",
      "50+ Workout Modes",
      "Smart Notifications",
      "Music Control",
    ],
    rating: 4.3,
    reviewCount: 89,
  },
  {
    id: "3",
    name: "Laptop Stand",
    image: "/laptop-stand.png",
    price: 49.99,
    description: "Ergonomic laptop stand for better posture and improved workspace setup.",
    category: "Accessories",
    brand: "ErgoDesk",
    inStock: true,
    stockQuantity: 50,
    images: [
      "/laptop-stand.png",
      "/laptop-stand-side.png",
      "/adjustable-laptop-stand.png",
      "/placeholder-e5suj.png",
    ],
    specifications: {
      Material: "Aluminum Alloy",
      Weight: "1.2kg",
      Compatibility: "10-17 inch laptops",
      "Height Adjustment": "6 levels",
      "Angle Adjustment": "0-45 degrees",
      "Load Capacity": "10kg",
    },
    features: [
      "Adjustable Height & Angle",
      "Heat Dissipation Design",
      "Non-slip Silicone Pads",
      "Portable & Foldable",
      "Cable Management",
      "Universal Compatibility",
    ],
    rating: 4.7,
    reviewCount: 203,
  },
  {
    id: "4",
    name: "Bluetooth Speaker",
    image: "/bluetooth-speaker.png",
    price: 79.99,
    description: "Portable Bluetooth speaker with excellent sound quality and waterproof design.",
    category: "Audio",
    brand: "SoundWave",
    inStock: true,
    stockQuantity: 30,
    images: [
      "/bluetooth-speaker.png",
      "/black-bluetooth-speaker.png",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    specifications: {
      "Output Power": "20W",
      "Battery Life": "12 hours",
      "Bluetooth Range": "30 meters",
      "Water Rating": "IPX7",
      "Charging Time": "3 hours",
      Dimensions: "180 x 65 x 65mm",
    },
    features: [
      "360° Surround Sound",
      "IPX7 Waterproof",
      "TWS Pairing",
      "Built-in Microphone",
      "LED Light Show",
      "USB-C Fast Charging",
    ],
    rating: 4.4,
    reviewCount: 156,
  },
  {
    id: "5",
    name: "USB-C Hub",
    image: "/usb-hub.png",
    price: 39.99,
    description: "Multi-port USB-C hub with HDMI, USB 3.0, and charging capabilities.",
    category: "Accessories",
    brand: "ConnectPro",
    inStock: true,
    stockQuantity: 40,
    images: [
      "/usb-hub.png",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    specifications: {
      Ports: "7-in-1",
      "HDMI Output": "4K@60Hz",
      "USB Ports": "3x USB 3.0",
      "Power Delivery": "100W",
      "Data Transfer": "5Gbps",
      Compatibility: "USB-C devices",
    },
    features: [
      "4K HDMI Output",
      "100W Power Delivery",
      "High-Speed Data Transfer",
      "Plug & Play",
      "Compact Design",
      "Heat Dissipation",
    ],
    rating: 4.2,
    reviewCount: 94,
  },
  {
    id: "6",
    name: "Wireless Mouse",
    image: "/wireless-mouse.png",
    price: 29.99,
    description: "Ergonomic wireless mouse with precision tracking and long battery life.",
    category: "Accessories",
    brand: "ClickTech",
    inStock: true,
    stockQuantity: 60,
    images: [
      "/wireless-mouse.png",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    specifications: {
      DPI: "1600",
      "Battery Life": "18 months",
      Connectivity: "2.4GHz Wireless",
      Range: "10 meters",
      Buttons: "3 buttons + scroll wheel",
      Compatibility: "Windows, Mac, Linux",
    },
    features: [
      "Ergonomic Design",
      "Precision Tracking",
      "Long Battery Life",
      "Plug & Play",
      "Silent Clicks",
      "Nano Receiver",
    ],
    rating: 4.1,
    reviewCount: 67,
  },
  {
    id: "7",
    name: "Phone Case",
    image: "/colorful-phone-case-display.png",
    price: 19.99,
    description: "Protective phone case with shock absorption and wireless charging compatibility.",
    category: "Mobile Accessories",
    brand: "GuardCase",
    inStock: true,
    stockQuantity: 100,
    images: [
      "/colorful-phone-case-display.png",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    specifications: {
      Material: "TPU + PC",
      "Drop Protection": "6 feet",
      Compatibility: "iPhone 14/15 Series",
      Thickness: "1.2mm",
      Weight: "25g",
      Colors: "6 available",
    },
    features: [
      "Shock Absorption",
      "Wireless Charging Compatible",
      "Raised Edges Protection",
      "Anti-Yellowing",
      "Easy Installation",
      "Precise Cutouts",
    ],
    rating: 4.6,
    reviewCount: 312,
  },
  {
    id: "8",
    name: "Desk Lamp",
    image: "/modern-desk-lamp.png",
    price: 59.99,
    description: "LED desk lamp with adjustable brightness and color temperature settings.",
    category: "Lighting",
    brand: "BrightDesk",
    inStock: true,
    stockQuantity: 20,
    images: [
      "/modern-desk-lamp.png",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
      "/placeholder.svg?height=400&width=400",
    ],
    specifications: {
      "LED Power": "12W",
      "Color Temperature": "3000K-6500K",
      "Brightness Levels": "10 levels",
      "Arm Length": "40cm",
      "Base Diameter": "18cm",
      "Power Source": "USB-C",
    },
    features: [
      "Adjustable Brightness",
      "Color Temperature Control",
      "Memory Function",
      "Touch Controls",
      "Eye Care Technology",
      "Flexible Arm",
    ],
    rating: 4.3,
    reviewCount: 78,
  },
]

// Mock reviews data
const mockReviews: ProductReview[] = [
  {
    id: "rev1",
    productId: "1",
    userId: "user1",
    userName: "John Smith",
    userPhoto: "/placeholder.svg?height=40&width=40",
    rating: 5,
    title: "Excellent sound quality!",
    comment:
      "These headphones exceeded my expectations. The noise cancellation is fantastic and the battery life is as advertised. Highly recommend!",
    createdAt: new Date("2024-01-10"),
    helpful: 12,
  },
  {
    id: "rev2",
    productId: "1",
    userId: "user2",
    userName: "Sarah Johnson",
    userPhoto: "/placeholder.svg?height=40&width=40",
    rating: 4,
    title: "Great value for money",
    comment:
      "Good headphones for the price. The build quality is solid and they're comfortable for long listening sessions.",
    createdAt: new Date("2024-01-08"),
    helpful: 8,
  },
  {
    id: "rev3",
    productId: "2",
    userId: "user3",
    userName: "Mike Chen",
    userPhoto: "/placeholder.svg?height=40&width=40",
    rating: 5,
    title: "Perfect fitness companion",
    comment:
      "This smartwatch has all the features I need for tracking my workouts. The GPS is accurate and the heart rate monitor works great.",
    createdAt: new Date("2024-01-12"),
    helpful: 15,
  },
]

export async function getProducts(): Promise<Product[]> {
  // Return mock data if Firebase is not configured
  if (!isFirebaseConfigured) {
    console.log("Using mock data - Firebase not configured")
    // Simulate network delay
    await new Promise((resolve) => setTimeout(resolve, 1000))
    return mockProducts
  }

  try {
    const productsCollection = collection(db, "products")
    const productsSnapshot = await getDocs(productsCollection)
    const products = productsSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...convertFirestoreData(doc.data()),
    })) as Product[]

    // If no products in Firestore, return mock data
    if (products.length === 0) {
      console.log("No products found in Firestore, using mock data")
      return mockProducts
    }

    return products
  } catch (error) {
    console.error("Error fetching products from Firestore:", error)
    console.log("Falling back to mock data")
    return mockProducts
  }
}

export async function getProduct(id: string): Promise<Product | null> {
  // Return mock data if Firebase is not configured
  if (!isFirebaseConfigured) {
    console.log("Using mock data - Firebase not configured")
    return mockProducts.find((product) => product.id === id) || null
  }

  try {
    const productDoc = doc(db, "products", id)
    const productSnapshot = await getDoc(productDoc)

    if (productSnapshot.exists()) {
      return {
        id: productSnapshot.id,
        ...convertFirestoreData(productSnapshot.data()),
      } as Product
    }

    // Fallback to mock data if not found in Firestore
    return mockProducts.find((product) => product.id === id) || null
  } catch (error) {
    console.error("Error fetching product from Firestore:", error)
    // Fallback to mock data
    return mockProducts.find((product) => product.id === id) || null
  }
}

export async function getProductReviews(productId: string): Promise<ProductReview[]> {
  // Return mock reviews for demo
  if (!isFirebaseConfigured) {
    return mockReviews.filter((review) => review.productId === productId)
  }

  try {
    // In a real app, you would fetch reviews from Firestore
    // For now, return mock data
    return mockReviews.filter((review) => review.productId === productId)
  } catch (error) {
    console.error("Error fetching reviews:", error)
    return []
  }
}

export async function getRelatedProducts(productId: string, category?: string): Promise<Product[]> {
  const allProducts = await getProducts()

  // Filter out the current product and get products from the same category
  let related = allProducts.filter(
    (product) => product.id !== productId && (category ? product.category === category : true),
  )

  // If no category match, get random products
  if (related.length === 0) {
    related = allProducts.filter((product) => product.id !== productId)
  }

  // Return up to 4 related products
  return related.slice(0, 4)
}
