import { collection, addDoc, getDocs, doc, updateDoc, serverTimestamp, query, orderBy, where } from "firebase/firestore"
import { db, isFirebaseConfigured } from "./firebase"
import type { Order, OrderItem, ShippingInfo, CartItem } from "./types"

// Generate a unique order number
export function generateOrderNumber(): string {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `ORD-${timestamp.slice(-6)}${random}`
}

// Convert cart items to order items
export function convertCartToOrderItems(cartItems: CartItem[]): OrderItem[] {
  return cartItems.map(item => ({
    id: item.id,
    name: item.name,
    image: item.image,
    price: item.price,
    quantity: item.quantity,
    total: item.price * item.quantity
  }))
}

// Calculate order totals
export function calculateOrderTotals(items: OrderItem[], shippingCost: number = 0, taxRate: number = 0) {
  const subtotal = items.reduce((sum, item) => sum + item.total, 0)
  const tax = subtotal * taxRate
  const total = subtotal + shippingCost + tax
  
  return {
    subtotal,
    shipping: shippingCost,
    tax,
    total
  }
}

// Create a new order
export async function createOrder(
  items: CartItem[],
  shippingInfo: ShippingInfo,
  userId?: string,
  userEmail?: string
): Promise<string> {
  if (!isFirebaseConfigured || !db) {
    throw new Error("Firebase not configured")
  }

  const orderItems = convertCartToOrderItems(items)
  const subtotal = orderItems.reduce((sum, item) => sum + item.total, 0)
  const shipping = subtotal > 50 ? 0 : 9.99
  const tax = 0 // You can implement tax calculation based on location
  const total = subtotal + shipping + tax

  const orderData: any = {
    orderNumber: generateOrderNumber(),
    items: orderItems,
    shippingInfo,
    subtotal,
    shipping,
    tax,
    total,
    status: 'processing',
    paymentStatus: 'pending',
    isGuestOrder: !userId,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
  }

  // Only add userId and userEmail if they have values
  if (userId) {
    orderData.userId = userId
  }
  if (userEmail) {
    orderData.userEmail = userEmail
  }

  try {
    const ordersCollection = collection(db, "orders")
    const docRef = await addDoc(ordersCollection, orderData)
    return docRef.id
  } catch (error) {
    console.error("Error creating order:", error)
    throw error
  }
}

// Get all orders (for admin)
export async function getAllOrders(): Promise<Order[]> {
  if (!isFirebaseConfigured || !db) {
    console.log("Firebase not configured, returning mock data")
    return getMockOrders()
  }

  try {
    const ordersCollection = collection(db, "orders")
    const ordersQuery = query(ordersCollection, orderBy("createdAt", "desc"))
    const ordersSnapshot = await getDocs(ordersQuery)
    
    const orders = ordersSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...convertFirestoreData(doc.data()),
    })) as Order[]

    // If no orders in Firestore, return mock data
    if (orders.length === 0) {
      console.log("No orders found in Firestore, using mock data")
      return getMockOrders()
    }

    return orders
  } catch (error) {
    console.error("Error fetching orders:", error)
    console.log("Falling back to mock data")
    return getMockOrders()
  }
}

// Get orders for a specific user
export async function getUserOrders(userId: string): Promise<Order[]> {
  if (!isFirebaseConfigured || !db) {
    console.log("Firebase not configured, returning mock data")
    return getMockOrders().filter(order => order.userId === userId)
  }

  try {
    const ordersCollection = collection(db, "orders")
    const userOrdersQuery = query(
      ordersCollection, 
      where("userId", "==", userId),
      orderBy("createdAt", "desc")
    )
    const ordersSnapshot = await getDocs(userOrdersQuery)
    
    const orders = ordersSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...convertFirestoreData(doc.data()),
    })) as Order[]

    return orders
  } catch (error) {
    console.error("Error fetching user orders:", error)
    return []
  }
}

// Update order status
export async function updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
  if (!isFirebaseConfigured || !db) {
    throw new Error("Firebase not configured")
  }

  try {
    const orderRef = doc(db, "orders", orderId)
    await updateDoc(orderRef, {
      status,
      updatedAt: serverTimestamp()
    })
  } catch (error) {
    console.error("Error updating order status:", error)
    throw error
  }
}

// Helper function to convert Firestore data to plain objects
function convertFirestoreData(data: any): any {
  const converted = { ...data }
  
  // Convert Firestore Timestamps to ISO strings
  Object.keys(converted).forEach(key => {
    if (converted[key] && typeof converted[key].toDate === 'function') {
      converted[key] = converted[key].toDate().toISOString()
    }
  })
  
  return converted
}

// Mock orders for development/fallback
function getMockOrders(): Order[] {
  return [
    {
      id: "mock-order-1",
      orderNumber: "ORD-001234",
      items: [
        {
          id: "1",
          name: "Wireless Headphones",
          image: "/wireless-headphones.png",
          price: 99.99,
          quantity: 1,
          total: 99.99
        }
      ],
      shippingInfo: {
        name: "John Doe",
        phone: "+1234567890",
        address: "123 Main St, City, State 12345",
        notes: "Leave at door"
      },
      subtotal: 99.99,
      shipping: 0,
      tax: 0,
      total: 99.99,
      status: 'delivered',
      paymentStatus: 'paid',
      isGuestOrder: true,
      userEmail: "<EMAIL>",
      createdAt: new Date("2024-01-15").toISOString(),
      updatedAt: new Date("2024-01-15").toISOString()
    },
    {
      id: "mock-order-2",
      orderNumber: "ORD-001235",
      items: [
        {
          id: "2",
          name: "Smart Watch",
          image: "/smartwatch-lifestyle.png",
          price: 199.99,
          quantity: 1,
          total: 199.99
        }
      ],
      shippingInfo: {
        name: "Jane Smith",
        phone: "+1987654321",
        address: "456 Oak Ave, Town, State 67890"
      },
      subtotal: 199.99,
      shipping: 0,
      tax: 0,
      total: 199.99,
      status: 'processing',
      paymentStatus: 'paid',
      isGuestOrder: false,
      userId: "user123",
      userEmail: "<EMAIL>",
      createdAt: new Date("2024-01-14").toISOString(),
      updatedAt: new Date("2024-01-14").toISOString()
    }
  ]
}
