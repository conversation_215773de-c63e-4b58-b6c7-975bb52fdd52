import { getProducts } from "@/lib/firestore"
import { isFirebaseConfigured } from "@/lib/firebase"
import ProductGrid from "../components/ProductGrid"

export default async function ShopPage() {
  let products = []
  let error = null
  const usingMockData = !isFirebaseConfigured

  try {
    products = await getProducts()
  } catch (err) {
    error = "Failed to load products. Please try again later."
    console.error("Error in ShopPage:", err)
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Firebase Status Banner */}
      {usingMockData && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
          <p className="text-sm text-yellow-800">
            <strong>Demo Mode:</strong> Firebase is not configured. Showing mock products for demonstration.
          </p>
        </div>
      )}

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Our Products</h1>
        <p className="text-lg text-gray-600">
          Discover our complete collection of amazing products
          {usingMockData && " (Demo Data)"}
        </p>
      </div>

      {error ? (
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <p className="text-red-600 mb-4">{error}</p>
            <p className="text-sm text-gray-600">
              Make sure your Firebase configuration is set up correctly and you have products in your Firestore
              database.
            </p>
          </div>
        </div>
      ) : (
        <ProductGrid products={products} />
      )}
    </div>
  )
}
