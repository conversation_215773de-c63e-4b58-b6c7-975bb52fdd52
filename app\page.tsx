import Link from "next/link";
import Image from "next/image";
import {
  <PERSON>R<PERSON>,
  Leaf,
  Shield,
  Truck,
  Heart,
  Sparkles,
  Star,
  Award,
  CheckCircle,
  Play,
} from "lucide-react";
import { getProducts } from "@/lib/firestore";
import ProductCard from "./components/ProductCard";
import HerbalVillageInfo from "./components/HerbalVillageInfo";
import HerbalPlantsTable from "./components/HerbalPlantsTable";
import VisitingGuide from "./components/VisitingGuide";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Product } from "@/lib/types";

export default async function HomePage() {
  // Fetch featured products (first 4 products)
  let featuredProducts: Product[] = [];

  try {
    const allProducts = await getProducts();
    featuredProducts = allProducts.slice(0, 4);
  } catch (error) {
    console.error("Error fetching featured products:", error);
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      {/* Enhanced Hero Section with New Features */}
      <section className="relative overflow-hidden pt-20 pb-20 flex items-center">
        {/* Dynamic Gradient Background */}
        <div className="absolute inset-0 z-0 bg-gradient-to-br from-green-50 via-white to-green-100 animate-gradient-x"></div>

        {/* Top Fade Grid Background */}
        <div
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: `linear-gradient(to right, #94a3b8 1px, transparent 1px),
            linear-gradient(to bottom, #94a3b8 1px, transparent 1px)`,
            backgroundSize: "40px 40px",
            WebkitMaskImage:
              "linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.15) 50%, transparent 100%)",
            maskImage:
              "linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.15) 50%, transparent 100%)",
          }}
        />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              {/* Enhanced Brand Badge with Animation */}
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/70 backdrop-blur-md border border-[#10c255]/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className="relative">
                  <Leaf className="h-5 w-5 text-[#10c255] animate-pulse" />
                  <div className="absolute inset-0 bg-[#10c255]/20 rounded-full animate-ping"></div>
                </div>
                <span
                  className="text-sm font-medium text-gray-700"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  100% প্রাকৃতিক ও সার্টিফাইড
                </span>
                <Award className="h-4 w-4 text-yellow-500" />
              </div>

              {/* Animated Title */}
              <div className="space-y-1">
                <h1
                  className="text-5xl lg:text-7xl font-bold text-[#10c255] animate-fade-in drop-shadow-sm"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  ঔষধি গ্রাম
                </h1>
                <div className="flex items-center gap-2">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>
                  <span
                    className="text-sm text-gray-600"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    ৪.৯ (২৫০০+ রিভিউ)
                  </span>
                </div>
              </div>

              <p
                className="text-xl text-gray-600 leading-relaxed"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                প্রকৃতির শক্তিতে সুস্থ জীবন। আমাদের সম্পূর্ণ প্রাকৃতিক ও ভেষজ
                পণ্যের সাথে আপনার স্বাস্থ্যকে রক্ষা করুন। বিশুদ্ধ, নিরাপদ এবং
                কার্যকর।
              </p>

              {/* Enhanced Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-[#10c255] hover:bg-[#0ea048] text-white rounded-full px-8 shadow-xl shadow-[#10c255]/20 transition-all hover:shadow-2xl hover:shadow-[#10c255]/30 hover:scale-105 group"
                  asChild
                >
                  <Link
                    href="/shop"
                    className="flex items-center justify-center gap-2"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    <span className="leading-none translate-y-0.5">
                      পণ্য দেখুন
                    </span>
                    <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform flex-shrink-0" />
                  </Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="rounded-full px-8 border-2 border-[#10c255]/30 hover:bg-[#10c255]/10 backdrop-blur-sm group flex items-center justify-center gap-2"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  <Play className="h-5 w-5 group-hover:scale-110 transition-transform flex-shrink-0" />
                  <span className="leading-none translate-y-0.5">
                    ভিডিও দেখুন
                  </span>
                </Button>
              </div>

              {/* Enhanced Trust Badges with Icons */}
              <div className="flex flex-wrap gap-3 pt-4">
                <Badge
                  variant="secondary"
                  className="px-4 py-2 bg-white/70 backdrop-blur-sm hover:bg-white/80 transition-all cursor-pointer"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  <Shield className="h-4 w-4 mr-2 text-blue-500" />
                  FDA অনুমোদিত
                </Badge>
                <Badge
                  variant="secondary"
                  className="px-4 py-2 bg-white/70 backdrop-blur-sm hover:bg-white/80 transition-all cursor-pointer"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  <Sparkles className="h-4 w-4 mr-2 text-purple-500" />
                  ল্যাব টেস্টেড
                </Badge>
                <Badge
                  variant="secondary"
                  className="px-4 py-2 bg-white/70 backdrop-blur-sm hover:bg-white/80 transition-all cursor-pointer"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  <Heart className="h-4 w-4 mr-2 text-red-500" />
                  ১০০% প্রাকৃতিক
                </Badge>
                <Badge
                  variant="secondary"
                  className="px-4 py-2 bg-white/70 backdrop-blur-sm hover:bg-white/80 transition-all cursor-pointer"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  মানি ব্যাক গ্যারান্টি
                </Badge>
              </div>
            </div>

            {/* Enhanced Hero Image with Multiple Features */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-[#10c255]/20 to-[#10c255]/20 rounded-3xl blur-3xl animate-pulse"></div>

              {/* Main Product Card */}
              <Card className="relative backdrop-blur-xl bg-white/50 border-[#10c255]/20 shadow-2xl rounded-3xl overflow-hidden hover:shadow-3xl transition-all duration-500 group">
                <CardContent className="p-0">
                  <div className="relative h-[500px]">
                    <Image
                      src="/logo/logo.png"
                      alt="Natural Herbal Products"
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                      priority
                    />

                    {/* Video Play Button Overlay */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <Button
                        size="lg"
                        className="rounded-full bg-white/90 text-[#10c255] hover:bg-white shadow-2xl"
                      >
                        <Play className="h-8 w-8" />
                      </Button>
                    </div>

                    {/* Enhanced Overlay Stats - Only Natural */}
                    <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/70 to-transparent">
                      <div className="flex justify-end">
                        <div className="text-center">
                          <p
                            className="text-2xl font-bold animate-pulse text-white"
                            style={{
                              fontFamily: "Noto Sans Bengali, sans-serif",
                            }}
                          >
                            ১০০%
                          </p>
                          <p
                            className="text-sm opacity-90 text-white"
                            style={{
                              fontFamily: "Noto Sans Bengali, sans-serif",
                            }}
                          >
                            প্রাকৃতিক
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Floating Testimonial Card */}
              <Card className="absolute -bottom-6 -left-6 w-80 backdrop-blur-xl bg-white/90 border-[#10c255]/20 shadow-xl rounded-2xl animate-float">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-[#10c255] to-[#10c255] flex items-center justify-center text-white font-bold">
                      র
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-1 mb-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className="h-3 w-3 text-yellow-400 fill-current"
                          />
                        ))}
                      </div>
                      <p
                        className="text-sm text-gray-700 mb-1"
                        style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                      >
                        "অসাধারণ পণ্য! সত্যিই কাজ করে।"
                      </p>
                      <p
                        className="text-xs text-gray-500"
                        style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                      >
                        - রহিমা খাতুন, ঢাকা
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Floating Achievement Badge */}
              <div className="absolute -top-4 -right-4 animate-bounce">
                <Badge className="bg-yellow-400 text-yellow-900 px-3 py-2 shadow-lg">
                  <Award className="h-4 w-4 mr-1" />
                  <span style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}>
                    বেস্ট সেলার
                  </span>
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section with Glassy Cards */}
      <section className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge
              className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              কেন আমাদের বেছে নেবেন
            </Badge>
            <h2
              className="text-4xl font-bold text-gray-900 mb-4"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              প্রকৃতির সাথে আপনার সংযোগ
            </h2>
            <p
              className="text-lg text-gray-600 max-w-2xl mx-auto"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              আমরা বিশ্বাস করি প্রকৃতিতেই রয়েছে সকল রোগের সমাধান
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: <Leaf className="h-8 w-8" />,
                title: "১০০% প্রাকৃতিক",
                description:
                  "সম্পূর্ণ রাসায়নিক মুক্ত, শুধুমাত্র প্রকৃতির উপাদান ব্যবহার করে তৈরি",
                color: "bg-green-100 text-green-600",
              },
              {
                icon: <Shield className="h-8 w-8" />,
                title: "পরীক্ষিত ও নিরাপদ",
                description: "প্রতিটি পণ্য গুণমান পরীক্ষা করে বাজারজাত করা হয়",
                color: "bg-blue-100 text-blue-600",
              },
              {
                icon: <Truck className="h-8 w-8" />,
                title: "দ্রুত ডেলিভারি",
                description: "সারাদেশে ২-৩ দিনের মধ্যে হোম ডেলিভারি",
                color: "bg-purple-100 text-purple-600",
              },
            ].map((feature, index) => (
              <Card
                key={index}
                className="group hover:shadow-2xl transition-all duration-300 backdrop-blur-sm bg-white/60 border-[#10c255]/10 hover:border-[#10c255]/30"
              >
                <CardContent className="p-8 text-center">
                  <div
                    className={`${feature.color} w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}
                  >
                    {feature.icon}
                  </div>
                  <h3
                    className="text-xl font-semibold mb-3"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {feature.title}
                  </h3>
                  <p
                    className="text-gray-600 leading-relaxed"
                    style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                  >
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-gradient-to-b from-white to-green-50/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge
              className="mb-4 bg-[#10c255]/10 text-[#10c255] border-[#10c255]/20"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              জনপ্রিয় পণ্য
            </Badge>
            <h2
              className="text-4xl font-bold text-gray-900 mb-4"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              আমাদের বিশেষ ভেষজ সংগ্রহ
            </h2>
            <p
              className="text-lg text-gray-600"
              style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
            >
              সবচেয়ে কার্যকর ও জনপ্রিয় প্রাকৃতিক পণ্যসমূহ
            </p>
          </div>

          {featuredProducts.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {featuredProducts.map((product) => (
                <div key={product.id} className="group">
                  <Card className="overflow-hidden backdrop-blur-sm bg-white/80 border-[#10c255]/10 hover:border-[#10c255]/30 transition-all hover:shadow-xl">
                    <ProductCard product={product} />
                  </Card>
                </div>
              ))}
            </div>
          ) : (
            <Card className="py-12 backdrop-blur-sm bg-white/60 border-[#10c255]/10">
              <CardContent className="text-center">
                <Leaf className="h-12 w-12 mx-auto mb-4 text-[#10c255]/50" />
                <p
                  className="text-gray-500"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  শীঘ্রই আসছে আমাদের ভেষজ পণ্যের সংগ্রহ
                </p>
              </CardContent>
            </Card>
          )}

          <div className="text-center">
            <Button
              size="lg"
              className="bg-[#10c255] hover:bg-[#0ea048] text-white rounded-full px-8"
              asChild
            >
              <Link
                href="/shop"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                সকল পণ্য দেখুন
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      <section>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <HerbalVillageInfo />
        </div>
      </section>


      <section>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <HerbalPlantsTable />
        </div>
      </section>

      <section>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <VisitingGuide />
        </div>
      </section>

      {/* CTA Section with Glassy Effect */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-[#10c255]/10 to-[#10c255]/10"></div>
        <div className="max-w-4xl mx-auto px-4">
          <Card className="backdrop-blur-xl bg-white/40 border-[#10c255]/20 shadow-2xl">
            <CardContent className="p-12 text-center">
              <Sparkles className="h-12 w-12 mx-auto mb-6 text-[#10c255]" />
              <h2
                className="text-3xl font-bold mb-4"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                প্রকৃতির সাথে শুরু করুন আপনার সুস্থ জীবন
              </h2>
              <p
                className="text-xl mb-8 text-gray-600"
                style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
              >
                আজই যুক্ত হোন হাজারো সন্তুষ্ট গ্রাহকের সাথে
              </p>
              <Button
                size="lg"
                className="bg-[#10c255] hover:bg-[#0ea048] text-white rounded-full px-10 shadow-xl shadow-[#10c255]/20"
                asChild
              >
                <Link
                  href="/shop"
                  style={{ fontFamily: "Noto Sans Bengali, sans-serif" }}
                >
                  এখনই কিনুন
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
